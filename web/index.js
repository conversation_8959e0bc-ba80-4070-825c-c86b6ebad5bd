// @ts-check
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";

import swaggerUi from "swagger-ui-express";
import YAML from "yamljs";
import { join } from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";

import shopify from "./shopify.js";
import productCreator from "./product-creator.js";
import PrivacyWebhookHandlers from "./privacy.js";
import draftOrderRoutes from "./backend/routes/draftOrderRoutes.js";
import { errorHandler } from "./backend/middlewares/errorHandler.js";
import CustomerRouter from "./backend/routes/customerRoutes.js";
import OrderRouter from "./backend/routes/orderRoutes.js";

import * as dotenv from "dotenv";
dotenv.config();

const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000",
  10
);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();
app.use(express.json());

// Set up Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

app.get("/api/healthcheck", (req, res) => {
  res.status(200).json({
    status: "OK",
    message: "Healthcheck Successfull",
  });
});

app.use("/api/draft-orders", draftOrderRoutes);
app.use("/api/customers", CustomerRouter);
app.use("/api/orders", OrderRouter);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const customerSwaggerDocument = YAML.load(
  join(__dirname, "../docs/swagger/customer-api.yaml")
);
const orderSwaggerDocument = YAML.load(
  join(__dirname, "../docs/swagger/order-api.yaml")
);
const draftOrderSwaggerDocument = YAML.load(
  join(__dirname, "../docs/swagger/draft-order-api.yaml")
);

// Merge the swagger documents
const combinedSwaggerDocument = {
  openapi: customerSwaggerDocument.openapi,
  info: {
    title: "Diamond HQ App APIs",
    description: "Combined API documentation for Customers and Orders",
    version: "1.0.0",
  },
  servers: customerSwaggerDocument.servers,
  tags: [
    {
      name: "Customers",
      description: "Customer management endpoints",
    },
    {
      name: "Orders",
      description: "Order management endpoints",
    },
    {
      name: "Draft Orders",
      description: "Draft Order management endpoints",
    },
  ],
  paths: {
    ...customerSwaggerDocument.paths,
    ...orderSwaggerDocument.paths,
    ...draftOrderSwaggerDocument.paths,
  },
  components: {
    schemas: {
      ...customerSwaggerDocument.components.schemas,
      ...orderSwaggerDocument.components.schemas,
      ...draftOrderSwaggerDocument.components.schemas,
    },
  },
};

app.use("/api/docs", swaggerUi.serve, swaggerUi.setup(combinedSwaggerDocument));

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js

app.use("/api/*", shopify.validateAuthenticatedSession());

app.get("/api/products/count", async (_req, res) => {
  const client = new shopify.api.clients.Graphql({
    session: res.locals.shopify.session,
  });

  const countData = await client.request(`
    query shopifyProductCount {
      productsCount {
        count
      }
    }
  `);

  res.status(200).send({ count: countData.data.productsCount.count });
});

app.post("/api/products", async (_req, res) => {
  let status = 200;
  let error = null;

  try {
    await productCreator(res.locals.shopify.session);
  } catch (e) {
    console.log(`Failed to process products/create: ${e.message}`);
    status = 500;
    error = e.message;
  }
  res.status(status).send({ success: status === 200, error });
});

// Add error handling middleware after all API routes but before static file serving
app.use(errorHandler);

app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

// @ts-ignore
app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(
      readFileSync(join(STATIC_PATH, "index.html"))
        .toString()
        .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
    );
});

app.listen(PORT);
