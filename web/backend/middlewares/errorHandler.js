export class AppError extends Error {
  constructor(errors, statusCode) {
    super(JSON.stringify(errors));
    this.statusCode = statusCode;
    this.errors = errors;
  }
}

export const errorHandler = (err, req, res, next) => {
  console.error("Error:", err);

  // Handle AppError instances
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      responseCode: 1,
      status: "error",
      errors: err.errors,
    });
  }

  // Handle validation errors (if using express-validator)
  if (err.array && typeof err.array === "function") {
    return res.status(400).json({
      responseCode: 1,
      status: "error",
      errors: err.array().map((error) => ({
        code: error.param.toUpperCase() + "_VALIDATION_ERROR",
        message: error.msg,
      })),
    });
  }

  // Handle other errors
  res.status(err.statusCode || 500).json({
    responseCode: 1,
    status: "error",
    errors: [
      {
        code: "INTERNAL_SERVER_ERROR",
        message:
          process.env.NODE_ENV === "production"
            ? "Something went wrong"
            : err.message,
      },
    ],
  });
};
