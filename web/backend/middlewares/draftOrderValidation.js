import { AppError } from "./errorHandler.js";

const validateCartItems = (items) => {
  const errors = [];

  if (!Array.isArray(items) || items.length === 0) {
    errors.push({
      code: "Validation Error",
      message: "Cart items must be an array and cannot be empty.",
    });
  } else {
    for (const item of items) {
      if (typeof item !== "object" || item === null) {
        errors.push({
          code: "Validation Error",
          message: `Each item in the cart must be an object.`,
        });
        continue;
      }

      if (!("variant_id" in item) || !("quantity" in item)) {
        errors.push({
          code: "Validation Error",
          message: "Each item must have variant_id and quantity.",
        });
        continue;
      }

      if (
        typeof item.variant_id !== "string" ||
        item.variant_id.length !== 14 ||
        !/^\d{14}$/.test(item.variant_id)
      ) {
        errors.push({
          code: "Validation Error",
          message: `Invalid variant ID provided: ${item.variant_id}. Must be a string of exactly 14 numeric characters.`,
        });
      }

      if (!Number.isInteger(item.quantity) || item.quantity <= 0) {
        errors.push({
          code: "Validation Error",
          message: `Quantity must be a positive integer for variant ID ${item.variant_id}.`,
        });
      }
    }
  }

  return errors;
};

const validateShopifyId = (id, owner) => {
  if (!id) {
    throw new AppError(
      [
        {
          code: "Validation Error",
          message: `${owner} ID is required`,
        },
      ],
      400
    );
  }
  const validations = [
    {
      condition: !id.match(new RegExp(`^gid:\\/\\/shopify\\/${owner}\\/\\d+$`)),
      message: `Shopify ${owner.toLowerCase()} ID is not valid`,
    },
  ];

  const failedValidation = validations.find(
    (validation) => validation.condition
  );

  if (failedValidation) {
    throw new AppError(
      [
        {
          code: "Validation Error",
          message: failedValidation.message,
        },
      ],
      400
    );
  }
};

const validateAddress = (address, type = "shipping") => {
  if (!address) {
    throw new AppError(
      [
        {
          code: "Validation Error",
          message: `${type} address is required`,
        },
      ],
      400
    );
  }
  const requiredFields = [
    "firstName",
    "lastName",
    "address1",
    "countryCode",
    "state",
    "city",
    "pincode",
    "phone",
  ];

  const missingFields = requiredFields.filter((field) => !address[field]);

  if (missingFields.length > 0) {
    throw new AppError(
      [
        {
          code: "Validation Error",
          message: `Missing required ${type} address fields: ${missingFields.join(
            ", "
          )}`,
        },
      ],
      400
    );
  }
};

export const validateCartDraftOrder = (req, res, next) => {
  try {
    const items = req.body?.items;
    const errors = validateCartItems(items);

    if (errors.length > 0) {
      return res.status(422).json({
        responseCode: 1,
        status: "error",
        errors,
      });
    }

    next();
  } catch (error) {
    next(error);
  }
};

export const validateEndlessAisleDraftOrder = (req, res, next) => {
  try {
    const items = req.body?.items;
    const errors = validateCartItems(items);

    if (errors.length > 0) {
      return res.status(422).json({
        responseCode: 1,
        status: "error",
        errors,
      });
    }
    validateShopifyId(req.body.customerId, "Customer");
    validateAddress(req.body.shippingAddress);

    let billingAddress = req.body.billingAddress || req.body.shippingAddress;
    if (billingAddress) {
      validateAddress(billingAddress, "billing");
    } else {
      req.body.billingAddress = req.body.shippingAddress;
    }

    if (req.method === "PUT") {
      validateShopifyId(req.body.draftOrderId, "DraftOrder");
    }

    next();
  } catch (error) {
    next(error);
  }
};
