import shopify from "../../shopify.js";

export const manageDraftOrder = async (
  currentPricesEachVariant,
  partialPaymentForEachProductMap,
  shop,
  AT,
  { customerId, shippingAddress, billingAddress },
  draftOrderId
) => {
  const orderAttributes = [];

  const lineItems = currentPricesEachVariant.map((variant) => {
    const { variantId } = variant;
    const partialPayment = partialPaymentForEachProductMap.get(variantId);

    orderAttributes.push({
      key: variantId.split("/").pop(),
      value: JSON.stringify(partialPayment),
    });

    return {
      variantId,
      quantity: 1,
      priceOverride: partialPayment
        ? {
            amount: partialPayment.totalWithGst.toString(),
            currencyCode: "INR",
          }
        : null,
      customAttributes: prepareLineItemAttribues(variant),
    };
  });

  const variables = {
    input: {
      customerId: customerId ? customerId : null,
      lineItems,
      shippingAddress: shippingAddress
        ? {
            firstName: shippingAddress.firstName,
            lastName: shippingAddress.lastName,
            address1: shippingAddress.address1,
            address2: shippingAddress.address2 || "",
            city: shippingAddress.city,
            province: shippingAddress.state,
            countryCode: "IN",
            zip: shippingAddress.pincode,
            phone: `${shippingAddress.phone}`,
          }
        : null,
      billingAddress: billingAddress
        ? {
            firstName: billingAddress.firstName,
            lastName: billingAddress.lastName,
            address1: billingAddress.address1,
            address2: billingAddress.address2 || "",
            city: billingAddress.city,
            province: billingAddress.state,
            countryCode: "IN",
            zip: billingAddress.pincode,
            phone: `${billingAddress.phone}`,
          }
        : null,
      customAttributes: orderAttributes,
    },
  };

  if (draftOrderId) {
    variables["ownerId"] = draftOrderId;
  }

  try {
    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(draftOrderId ? UPDATE_DRAFT_ORDER : CREATE_DRAFT_ORDER, {
      variables: variables,
    });

    if (draftOrderId) {
      if (data.draftOrderUpdate.userErrors.length > 0) {
        console.error("User errors:", data.draftOrderUpdate.userErrors);
        return {
          success: false,
          errors: data.draftOrderUpdate.userErrors.map((x) => ({
            code: "SHOPIFY_VALIDATION_ERROR",
            message: x.message,
          })),
        };
      }

      return {
        success: true,
        draftOrder: data.draftOrderUpdate.draftOrder,
      };
    } else {
      if (data.draftOrderCreate.userErrors.length > 0) {
        console.error("User errors:", data.draftOrderCreate.userErrors);
        return {
          success: false,
          errors: data.draftOrderCreate.userErrors.map((x) => ({
            code: "SHOPIFY_VALIDATION_ERROR",
            message: x.message,
          })),
        };
      }

      return {
        success: true,
        draftOrder: data.draftOrderCreate.draftOrder,
      };
    }
  } catch (error) {
    console.error("Error:", error);
    return { success: false, error: error.message };
  }
};

export const prepareLineItemAttribues = ({
  metal,
  diamond,
  gemstone,
  solitaire,
}) => {
  return [
    { key: "metal_type", value: metal.type },
    { key: "metal_purity", value: metal.purity },
    { key: "metal_weight", value: metal.weight.toString() },
    { key: "metal_dhplPrice", value: metal.dhplPrice.toString() },
    { key: "metal_totalPrice", value: metal.totalMetalPrice.toString() },

    { key: "diamond_type", value: diamond.type },
    {
      key: "diamond_totalPrice",
      value: diamond.totalDiamondPrice.toString(),
    },
    ...(Array.isArray(diamond.breakdown)
      ? diamond.breakdown.flatMap((d, i) => [
          { key: `diamond_${i + 1}_range`, value: d.range },
          { key: `diamond_${i + 1}_weight`, value: d.weight.toString() },
          {
            key: `diamond_${i + 1}_pricePerCt`,
            value: d.pricePerCt.toString(),
          },
          { key: `diamond_${i + 1}_subtotal`, value: d.subtotal.toString() },
        ])
      : []),

    {
      key: "gemstone_totalPrice",
      value: gemstone.totalGemstonePrice.toString(),
    },
    ...(gemstone.breakdown
      ? [
          { key: "gemstone_type", value: gemstone.breakdown.type },
          { key: "gemstone_quality", value: gemstone.breakdown.quality },
          {
            key: "gemstone_carat",
            value: gemstone.breakdown.carat.toString(),
          },
          {
            key: "gemstone_ratePerCt",
            value: gemstone.breakdown.ratePerCt.toString(),
          },
          {
            key: "gemstone_subtotal",
            value: gemstone.breakdown.subtotal.toString(),
          },
        ]
      : []),

    {
      key: "solitaire_totalPrice",
      value: solitaire.totalSolitairePrice.toString(),
    },
    ...(Array.isArray(solitaire.breakdown)
      ? solitaire.breakdown.flatMap((s, i) => [
          { key: `solitaire_${i + 1}_shape`, value: s.shape },
          { key: `solitaire_${i + 1}_grade`, value: s.grade },
          { key: `solitaire_${i + 1}_carat`, value: s.carat.toString() },
          { key: `solitaire_${i + 1}_price`, value: s.price.toString() },
        ])
      : []),
  ];
};

const CREATE_DRAFT_ORDER = `
    mutation createDraftOrder($input: DraftOrderInput!) {
      draftOrderCreate(input: $input) {
        draftOrder {
          id
          invoiceUrl
          totalPrice
          customer {
            id
            email
            firstName
            lastName
            phone
          }
          subtotalPrice
          shippingLine {
            price
            code
            title
          }
          lineItems(first: 50) {
            nodes {
              id
              image {
                url
              }
              quantity
              title
              sku
              originalTotal
              variant {
                id
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }`;

const UPDATE_DRAFT_ORDER = `mutation updateDraftOrder($input: DraftOrderInput!, $ownerId: ID!) {
      draftOrderUpdate(input: $input, id: $ownerId) {
        draftOrder {
          id
          invoiceUrl
          totalPrice
          customer {
            id
            email
            firstName
            lastName
            phone
          }
          subtotalPrice
          shippingLine {
            price
            code
            title
          }
          lineItems(first: 50) {
            nodes {
              id
              image {
                url
              }
              quantity
              title
              sku
              originalTotal
              variant {
                id
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }`;
