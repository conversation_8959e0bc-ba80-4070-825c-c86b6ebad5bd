import { GET_VARIANT_METAFIELDS } from "./constants.js";
export const getMetafields = async (shop, AT, variantIds) => {
  try {
    const formattedIds = variantIds.map(
      (id) => `gid://shopify/ProductVariant/${id}`
    );

    const response = await fetch(
      `https://${shop}/admin/api/2023-04/graphql.json`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": AT,
        },
        body: JSON.stringify({
          query: GET_VARIANT_METAFIELDS,
          variables: { ids: formattedIds },
        }),
      }
    );

    const json = await response.json();

    // Assuming response data shape matches your example
    const variantExists = json.data?.nodes?.some((variant) => variant !== null);
    if (!variantExists) {
      throw new Error("Variants does not exist");
    }
    const cleanedData = json.data?.nodes?.map((variant) => {
      const cleanedVariant = { id: variant.id };
      Object.entries(variant).forEach(([key, value]) => {
        // Skip id (already added) and null values
        if (key !== "id" && value !== null) {
          cleanedVariant[key] = value;
        }
      });
      return cleanedVariant;
    });
    return cleanedData;
  } catch (error) {
    console.error("Error fetching metafields:", error);
    throw new Error(error);
  }
};
