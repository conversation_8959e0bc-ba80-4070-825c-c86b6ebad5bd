export const getMetalsToFetchPrice = (metafieldsArray) => {
  return metafieldsArray.reduce(
    (acc, metafields) => {
      const hasDiamondRound = Object.entries(metafields).some(
        ([key, value]) => key.startsWith("diamondRound") && value !== null
      );

      const hasDiamondOther = Object.entries(metafields).some(
        ([key, value]) => key.startsWith("diamondOther") && value !== null
      );

      return {
        metalType: acc.metalType || metafields.metalType?.value || false,
        diamondRound: acc.diamondRound || hasDiamondRound,
        diamondOther: acc.diamondOther || hasDiamondOther,
        solitaireShape1: acc.solitaireShape1 || !!metafields.solitaireShape1?.value,
        solitaireShape2: acc.solitaireShape2 || !!metafields.solitaireShape2?.value,
        solitaireShape3: acc.solitaireShape3 || !!metafields.solitaireShape3?.value,
        solitaireShape4: acc.solitaireShape4 || !!metafields.solitaireShape4?.value,
        solitaireShape5: acc.solitaireShape5 || !!metafields.solitaireShape5?.value,
        gemstoneType: acc.gemstoneType || metafields.gemstoneType?.value || false,
      };
    },
    {
      metalType: false,
      diamondRound: false,
      diamondOther: false,
      solitaireShape1: false,
      solitaireShape2: false,
      solitaireShape3: false,
      solitaireShape4: false,
      solitaireShape5: false,
      gemstoneType: false,
    }
  );
};
