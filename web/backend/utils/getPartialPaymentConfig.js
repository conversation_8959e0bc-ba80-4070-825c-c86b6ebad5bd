export const getPartialPaymentConfig = async () => {
  try {
    const response = await fetch(
      `${process.env.BACKEND_URL}/api/partial-payment-config`,
      {
        method: "GET",
        headers: {
          "Authorization": process.env.AUTH_TOKEN
        }
      }
    );
    const data = await response.json();

    if (data.responseCode === 0 && data.status === "success") {
      const config = data.data[0];

      // Helper function to convert percentage to decimal
      const toDecimal = (value) => {
        // If value is already in decimal form (e.g., 0.5)
        if (value >= 0 && value <= 1) {
          return value;
        }
        // If value is in percentage form (e.g., 50)
        if (value > 1) {
          return value / 100;
        }
        throw new Error(
          `Invalid value provided for partial payment config: ${value}`
        );
      };

      // Convert percentage values to decimal and validate
      return {
        metal: toDecimal(config.metal),
        making: toDecimal(config.making),
        studdedDiamonds: toDecimal(config.studdedDiamonds),
        solitaire: toDecimal(config.solitaire),
        gemstones: toDecimal(config.gemstones),
      };
    }
    throw new Error(
      "Failed to fetch partial payment configuration: Invalid response format"
    );
  } catch (error) {
    console.error("Error fetching partial payment config:", error);
    throw new Error(
      "Failed to fetch partial payment configuration. Please ensure the configuration API is available."
    );
  }
};
