export const GET_VARIANT_METAFIELDS = `
  query GetVariantMetafields($ids: [ID!]!) {
    nodes(ids: $ids) {
      ... on ProductVariant {
        id
        metalWeight: metafield(namespace: "custom", key: "metal_weight") { key value }
        metalType: metafield(namespace: "custom", key: "metal_") { key value }
        metalPurity: metafield(namespace: "custom", key: "purity") { key value }

        diamondClarity: metafield(namespace: "custom", key: "clarity") { key value }
        diamondColor: metafield(namespace: "custom", key: "diamond_color") { key value }
        diamondType: metafield(namespace: "custom", key: "diamond_type") { key value }

        diamondRound2_5: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_2_5") { key value }
        diamondRound2_5_5: metafield(namespace: "custom", key: "weight_of_studded_round_diamond_sieve_size_2_5_5") { key value }
        diamondRound5_8: metafield(namespace: "custom", key: "weight_of_studded_round_diamond_sieve_size_5_8") { key value }
        diamondRound8_11: metafield(namespace: "custom", key: "weight_of_studded_round_diamond_sieve_size_8_11") { key value }
        diamondRound11_12: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_11_12") { key value }
        diamondRound12_13: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_12_13") { key value }
        diamondRound13_14: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_13_14") { key value }
        diamondRound14_15: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_14_15") { key value }
        diamondRound15_16: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_15_16") { key value }
        diamondRound16_17: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_16_17") { key value }
        diamondRound17_18: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_17_18") { key value }
        diamondRound18_19: metafield(namespace: "custom", key: "weight_of_diamond_sieve_size_18_19") { key value }

        diamondOther2_5: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_2_5") { key value }
        diamondOther2_5_5: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_2_5_5") { key value }
        diamondOther5_8: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_5_8") { key value }
        diamondOther8_11: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_8_11") { key value }
        diamondOther11_12: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_11_12") { key value }
        diamondOther12_13: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_12_13") { key value }
        diamondOther13_14: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_13_14") { key value }
        diamondOther14_15: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_14_15") { key value }
        diamondOther15_16: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_15_16") { key value }
        diamondOther16_17: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_16_17") { key value }
        diamondOther17_18: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_17_18") { key value }
        diamondOther18_19: metafield(namespace: "custom", key: "weight_of_other_shape_diamond_sieve_size_18_19") { key value }

        
        
        gemstoneType: metafield(namespace: "custom", key: "gemstone_type") { key value }
        gemstoneQuality: metafield(namespace: "custom", key: "gemstone_quality_") { key value }
        gemstoneCarat: metafield(namespace: "custom", key: "gemstones_ct") { key value }

        solitaireColor1: metafield(namespace: "custom", key: "solitaire_color_1") { key value }
        solitaireClarity1: metafield(namespace: "custom", key: "solitaire_clarity_1") { key value }
        solitaireShape1: metafield(namespace: "custom", key: "solitaire_shape_1") { key value }
        solitaireCarat1: metafield(namespace: "custom", key: "solitaire_carat_1") { key value }

        solitaireColor2: metafield(namespace: "custom", key: "solitaire_color_2") { key value }
        solitaireClarity2: metafield(namespace: "custom", key: "solitaire_clarity_2") { key value }
        solitaireShape2: metafield(namespace: "custom", key: "solitaire_shape_2") { key value }
        solitaireCarat2: metafield(namespace: "custom", key: "solitaire_carat_2") { key value }

        solitaireColor3: metafield(namespace: "custom", key: "solitaire_color_3") { key value }
        solitaireClarity3: metafield(namespace: "custom", key: "solitaire_clarity_3") { key value }
        solitaireShape3: metafield(namespace: "custom", key: "solitaire_shape_3") { key value }
        solitaireCarat3: metafield(namespace: "custom", key: "solitaire_carat_3") { key value }

        solitaireColor4: metafield(namespace: "custom", key: "solitaire_color_4") { key value }
        solitaireClarity4: metafield(namespace: "custom", key: "solitaire_clarity_4") { key value }
        solitaireShape4: metafield(namespace: "custom", key: "solitaire_shape_4") { key value }
        solitaireCarat4: metafield(namespace: "custom", key: "solitaire_carat_4") { key value }

        solitaireColor5: metafield(namespace: "custom", key: "solitaire_color_5") { key value }
        solitaireClarity5: metafield(namespace: "custom", key: "solitaire_clarity_5") { key value }
        solitaireShape5: metafield(namespace: "custom", key: "solitaire_shape_5") { key value }
        solitaireCarat5: metafield(namespace: "custom", key: "solitaire_carat_5") { key value }

        totalPriceMetal: metafield(namespace: "custom", key: "total_price_of_metals") { key value }
        totalPriceDiamonds: metafield(namespace: "custom", key: "total_amount_of_diamonds") { key value }
        totalPriceSolitaire: metafield(namespace: "custom", key: "total_price_of_solitaire_store_") { key value }
        totalGemstonePrice: metafield(namespace: "custom", key: "total_gemstone_carat_amount") { key value }
        totalMakingCharges: metafield(namespace: "custom", key: "making_charges_") { key value }
        totalGst: metafield(namespace: "custom", key: "total_price_with_gst") { key value }
      }
    }
  }`;
