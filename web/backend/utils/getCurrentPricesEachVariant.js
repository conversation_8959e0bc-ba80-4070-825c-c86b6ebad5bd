export const getCurrentPricesEachVariant = (
  materialPrices,
  metafieldsWithVariantID
) => {
  if (!materialPrices?.metal?.data || !Array.isArray(metafieldsWithVariantID))
    return [];

  const sieveMappings = {
    "2_5": [0, 2.5],
    "2_5_5": [2.5, 5],
    "5_8": [5, 8],
    "8_11": [8, 11],
    "11_12": [11, 12],
    "12_13": [12, 13],
    "13_14": [13, 14],
    "14_15": [14, 15],
    "15_16": [15, 16],
    "16_17": [16, 17],
    "17_18": [17, 18],
    "18_19": [18, 19],
  };

  const getDiamondPrice = (diamondTypeKey, min, max, diamondType) => {
    const diamondList = materialPrices[diamondTypeKey]?.data || [];

    return diamondList.find(
      (item) =>
        item.diamondType === diamondType &&
        min >= item.minSize &&
        max <= item.maxSize
    );
  };

  const getGemstonePrice = (type, carat, quality) => {
    const gemstoneList =
      materialPrices?.[`gemstone${capitalize(type)}`]?.data || [];
    return gemstoneList.find(
      (item) =>
        carat >= item.minCarat &&
        carat <= item.maxCarat &&
        item.quality === quality
    );
  };

  const capitalize = (str) => str?.charAt(0).toUpperCase() + str?.slice(1);

  return metafieldsWithVariantID.map((variant) => {
    // --- Metal ---
    const metalType = variant?.metalType?.value;
    const metalPurity = variant?.metalPurity?.value;
    const metalWeight = parseFloat(variant?.metalWeight?.value || 0);

    const matchedMetal = materialPrices.metal.data.find(
      (metal) =>
        metal.metalName === metalType && metal.metalPurity === metalPurity
    );

    const dhplPrice = matchedMetal?.dhplPrice ?? 0;
    const totalMetalPrice = metalWeight * dhplPrice;

    // --- Diamonds ---
    const diamondTypeBase = variant?.diamondType?.value;
    const diamondColor = variant?.diamondColor?.value;
    const diamondClarity = variant?.diamondClarity?.value;
    const fullDiamondType = `${diamondTypeBase} ${diamondColor}-${diamondClarity}`;

    let totalDiamondPrice = 0;
    const diamondBreakdown = [];

    Object.entries(sieveMappings).forEach(([keySuffix, [min, max]]) => {
      const roundKey = `diamondRound${keySuffix}`;
      const otherKey = `diamondOther${keySuffix}`;

      const roundValue = parseFloat(variant?.[roundKey]?.value || 0);
      const otherValue = parseFloat(variant?.[otherKey]?.value || 0);

      if (roundValue > 0) {
        const matched = getDiamondPrice(
          "roundDiamond",
          min,
          max,
          fullDiamondType
        );
        const pricePerCt = matched?.sellingPrice ?? 0;
        totalDiamondPrice += roundValue * pricePerCt;
        diamondBreakdown.push({
          type: "roundDiamond",
          range: `${min}-${max}`,
          weight: roundValue,
          pricePerCt,
          subtotal: roundValue * pricePerCt,
        });
      }

      if (otherValue > 0) {
        const matched = getDiamondPrice(
          "otherDiamond",
          min,
          max,
          fullDiamondType
        );
        const pricePerCt = matched?.sellingPrice ?? 0;
        totalDiamondPrice += otherValue * pricePerCt;
        diamondBreakdown.push({
          type: "otherDiamond",
          range: `${min}-${max}`,
          weight: otherValue,
          pricePerCt,
          subtotal: otherValue * pricePerCt,
        });
      }
    });

    // --- Gemstones ---
    const gemstoneType = variant?.gemstoneType?.value;
    const gemstoneQuality = variant?.gemstoneQuality?.value;
    const gemstoneCarat = parseFloat(variant?.gemstoneCarat?.value || 0);

    let gemstoneRate = 0;
    let gemstoneSubtotal = 0;
    let gemstoneBreakdown = null;

    if (gemstoneType && gemstoneQuality && gemstoneCarat >= 0) {
      const matchedGem = getGemstonePrice(
        gemstoneType,
        gemstoneCarat,
        gemstoneQuality
      );
      gemstoneRate = matchedGem?.ratePerCt ?? 0;
      gemstoneSubtotal = gemstoneCarat * gemstoneRate;
      gemstoneBreakdown = {
        type: gemstoneType,
        quality: gemstoneQuality,
        carat: gemstoneCarat,
        ratePerCt: gemstoneRate,
        subtotal: gemstoneSubtotal,
      };
    }
    // --- Solitaire ---
let totalSolitairePrice = 0;
const solitaireBreakdown = [];

for (let i = 1; i <= 5; i++) {
  const shape = variant[`solitaireShape${i}`]?.value;
  const carat = parseFloat(variant[`solitaireCarat${i}`]?.value || 0);
  const color = variant[`solitaireColor${i}`]?.value;
  const clarity = variant[`solitaireClarity${i}`]?.value;

  if (!shape || !carat || !color || !clarity) continue;

  const grade = `${color}-${clarity}`;

  // Combine both price lists
  const combinedPrices = [
    ...(materialPrices?.roundSolitaire?.data || []),
    ...(materialPrices?.otherSolitaire?.data || [])
  ];

  const matched = combinedPrices.find(
    (item) =>
      item.grade === grade &&
      carat >= item.minCarat &&
      carat <= item.maxCarat
  );

  if (matched) {
    totalSolitairePrice += matched.price;
    solitaireBreakdown.push({
      shape,
      grade,
      carat,
      price: matched.price,
    });
  }
}

    return {
      variantId: variant.id,
      metal: {
        type: metalType,
        purity: metalPurity,
        weight: metalWeight,
        dhplPrice,
        totalMetalPrice,
      },
      diamond: {
        type: fullDiamondType,
        totalDiamondPrice,
        breakdown: diamondBreakdown,
      },
      gemstone: {
        totalGemstonePrice: gemstoneSubtotal,
        breakdown: gemstoneBreakdown,
      },
      solitaire: {
        totalSolitairePrice,
        breakdown: solitaireBreakdown,
      },
    };
  });
};
