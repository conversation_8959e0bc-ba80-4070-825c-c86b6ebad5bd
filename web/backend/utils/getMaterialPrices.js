export default async function fetchMaterialPrices(materialsToBeFetched) {
  const materialPrices = {};

  // Helper function to fetch data
  const fetchData = async (endpoint, key) => {
    try {
      const response = await fetch(`${process.env.BACKEND_URL}${endpoint}`, {
        method: "GET",
        redirect: "follow",
        headers: {
          "Authorization": process.env.AUTH_TOKEN
        },
      });
      const result = await response.json();
      if (result) {
        materialPrices[key] = result;
      }
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error);
    }
  };

  const fetchPromises = [];

  // Handle metal type fetching
  if (materialsToBeFetched.metalType) {
    const { metalType } = materialsToBeFetched;
    if (metalType === "Gold" || metalType === "Platinum") {
      fetchPromises.push(fetchData("/api/metal-price", "metal"));
    }
  }

  // Handle diamond-related fetching
  if (materialsToBeFetched.diamondRound) {
    fetchPromises.push(fetchData("/api/diamond-price/round", "roundDiamond"));
  }
  if (materialsToBeFetched.diamondOther) {
    fetchPromises.push(fetchData("/api/diamond-price/others", "otherDiamond"));
  }

  // Handle solitaire-related fetching
  const solitaireShapes = [
    materialsToBeFetched.solitaireShape1,
    materialsToBeFetched.solitaireShape2,
    materialsToBeFetched.solitaireShape3,
    materialsToBeFetched.solitaireShape4,
    materialsToBeFetched.solitaireShape5,
  ];
  const shapesSet = new Set(solitaireShapes.filter(Boolean)); // Filters out undefined/null
  // Fetch Round if present
  if (shapesSet.has("Round")) {
    fetchPromises.push(
      fetchData("/api/solitaire-price/round", "roundSolitaire")
    );
  }

  // Check if there's any shape other than "Round"
  const hasOther = [...shapesSet].some((shape) => shape !== "Round");
  if (hasOther) {
    fetchPromises.push(
      fetchData("/api/solitaire-price/others", "otherSolitaire")
    );
  }

  // Handle gemstone type fetching based on the value of gemstoneType
  const gemstoneType = materialsToBeFetched.gemstoneType;
  if (gemstoneType) {
    switch (gemstoneType) {
      case "smallStudded":
        fetchPromises.push(
          fetchData("/api/gemstone-price/smallStudded", "gemstoneSmallStudded")
        );
        break;
      case "semiPrecious":
        fetchPromises.push(
          fetchData("/api/gemstone-price/semiPrecious", "gemstoneSemiPrecious")
        );
        break;
      case "Precious":
        fetchPromises.push(
          fetchData("/api/gemstone-price/precious", "gemstonePrecious")
        );
        break;
      default:
        break;
    }
  }

  // Wait for all fetch promises to complete
  await Promise.all(fetchPromises);

  return materialPrices;
}
