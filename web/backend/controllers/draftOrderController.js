import { getATFromSQL } from "../utils/getToken.js";
import { getMetafields } from "../utils/getMetafields.js";
import { getMetalsToFetchPrice } from "../utils/getMetalsToFetchPrice.js";
import { getCurrentPricesEachVariant } from "../utils/getCurrentPricesEachVariant.js";
import fetchMaterialPrices from "../utils/getMaterialPrices.js";
import { manageDraftOrder } from "../utils/manageDraftOrder.js";
import { getPartialPaymentConfig } from "../utils/getPartialPaymentConfig.js";
import { AppError } from "../middlewares/errorHandler.js";
import shopify from "../../shopify.js";


// Helper function to calculate partial payment for a product
const calculatePartialPayment = (product, minCharges) => {
  const metal =
    parseFloat(product.totalPriceMetal?.value || 0) * minCharges.metal;
  const diamonds =
    parseFloat(product.totalPriceDiamonds?.value || 0) *
    minCharges.studdedDiamonds;
  const solitaire =
    parseFloat(product.totalPriceSolitaire?.value || 0) * minCharges.solitaire;
  const gemstones =
    parseFloat(product.totalGemstonePrice?.value || 0) * minCharges.gemstones;
  const making =
    parseFloat(product.totalMakingCharges?.value || 0) * minCharges.making;

  const subtotal = metal + diamonds + solitaire + gemstones + making;
  const gst = subtotal * 0.03;
  const totalWithGst = subtotal + gst;

  return {
    id: product.id,
    partialPayment: {
      metal,
      diamonds,
      solitaire,
      gemstones,
      making,
      subtotal,
      gst,
      totalWithGst,
    },
  };
};

// Core function to process draft order creation
const processDraftOrder = async (req, customerInfo = null, draftOrderId) => {
  // Get store access token
  const shop = process.env.SHOP;
  const storeData = await getATFromSQL();
  const AT = storeData.find((x) => x.shop === shop)?.accessToken;

  if (!AT) {
    throw new AppError(
      [
        {
          code: "Auth Error",
          message: "Access token not found for the specified shop.",
        },
      ],
      401
    );
  }

  const variantIds = req.body.items.map((item) => {
    return item.variant_id;
  });

  // Fetch all required data in parallel
  const [metafieldsWithVariantID, minCharges] = await Promise.all([
    getMetafields(shop, AT, variantIds),
    getPartialPaymentConfig(),
  ]);

  // Get materials and their prices
  const materialsToBeFetched = await getMetalsToFetchPrice(
    metafieldsWithVariantID
  );
  const materialPrices = await fetchMaterialPrices(materialsToBeFetched);

  // Calculate current prices and partial payments
  const currentPricesEachVariant = getCurrentPricesEachVariant(
    materialPrices,
    metafieldsWithVariantID
  );

  const partialPaymentForEachProduct = metafieldsWithVariantID.map((product) =>
    calculatePartialPayment(product, minCharges)
  );

  const partialPaymentForEachProductMap = new Map();
  partialPaymentForEachProduct.forEach((product) => {
    partialPaymentForEachProductMap.set(product.id, product.partialPayment);
  });
  // Create draft order with or without customer info
  const createPartialPaymentUrl = await manageDraftOrder(
    currentPricesEachVariant,
    partialPaymentForEachProductMap,
    shop,
    AT,
    customerInfo || {},
    draftOrderId
  );
  return createPartialPaymentUrl;
};

// Controller for regular draft order creation
export const handleDraftOrder = async (req, res, next) => {
  try {
    let customerInfo = null;
    // If the request is for an endless aisle, extract customer info
    if (req.route.path.startsWith("/endless-aisle")) {
      customerInfo = {
        customerId: req.body.customerId,
        shippingAddress: req.body.shippingAddress,
        billingAddress: req.body.billingAddress,
      };
    }

    const draftOrderResult = await processDraftOrder(
      req,
      customerInfo,
      req.method === "PUT" ? req.body.draftOrderId : null
    );

    // Send success response
    if (draftOrderResult.success) {
      res.status(200).json({
        responseCode: 0,
        status: "success",
        data: {
          draftOrder: draftOrderResult.draftOrder,
        },
      });
    } else {
      res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: draftOrderResult.errors,
      });
    }
  } catch (error) {
    next(error);
  }
};

export const sendDraftOrderInvoice = async (req, res) => {
  try {
    const draftOrderId = req.params.id;

    if (!draftOrderId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Draft Order ID is required.",
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(SEND_DRAFT_ORDER_INVOICE_MUTATION, {
      variables: {
        id: `gid://shopify/DraftOrder/${draftOrderId}`
      },
    });

    if (data.draftOrderInvoiceSend.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: data.draftOrderInvoiceSend.userErrors.map((error) => ({
          code: "VALIDATION_ERROR",
          message: error.message,
          field: error.field,
        })),
      });
    }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        draftOrder: data.draftOrderInvoiceSend.draftOrder,
      },
    });
  } catch (err) {
    console.error("Error sending draft order invoice:", err);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

export const createDraftOrderPayment = async (req, res) => {
  try {
    if (!req || !req.body || Object.keys(req.body).length === 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Request body cannot be empty",
          },
        ],
      });
    }

    const draftOrderId = req.params.id;
    const { transactionMethod, transactionDetails } = req.body;
    const validMethods = ['cash', 'upi', 'card'];

    // Combined validation for required fields and payment method
    if (!draftOrderId || !transactionMethod || !transactionDetails || 
        !validMethods.includes(transactionMethod.toLowerCase())) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: !validMethods.includes(transactionMethod.toLowerCase()) 
              ? "Invalid transaction method. Allowed methods are: cash, upi, card"
              : "Draft Order transaction method and transaction details are required.",
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "AUTH_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    // Check draft order status
    const { data: draftOrderData } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(GET_DRAFT_ORDER_STATUS, {
      variables: {
        id: `gid://shopify/DraftOrder/${draftOrderId}`
      }
    });

    if (!draftOrderData?.draftOrder) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: "Draft order not found",
          },
        ],
      });
    }

    if (draftOrderData.draftOrder.status === 'COMPLETED') {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Cannot update payment details for completed draft order",
          },
        ],
      });
    }

    // Update metafields
    const variables = {
      id: `gid://shopify/DraftOrder/${draftOrderId}`,
      input: {
        metafields: [
          {
            namespace: "custom",
            key: "transaction_method",
            type: "single_line_text_field",
            value: transactionMethod.trim()
          },
          {
            namespace: "custom",
            key: "transaction_details",
            type: "single_line_text_field",
            value: transactionDetails.trim()
          }
        ]
      }
    };

    const { data: metafieldsData } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(UPDATE_DRAFT_ORDER_PAYMENT_MUTATION, {
      variables
    });

    if (metafieldsData.draftOrderUpdate.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: metafieldsData.draftOrderUpdate.userErrors.map((error) => ({
          code: "VALIDATION_ERROR",
          message: error.message,
          field: error.field,
        })),
      });
    }

    // Complete the draft order
    const { data: completionData } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(COMPLETE_DRAFT_ORDER_MUTATION, {
      variables: {
        id: `gid://shopify/DraftOrder/${draftOrderId}`
      }
    });

    if (completionData.draftOrderComplete.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: completionData.draftOrderComplete.userErrors.map((error) => ({
          code: "VALIDATION_ERROR",
          message: error.message,
          field: error.field,
        })),
      });
    }
    
    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        draftOrder: completionData.draftOrderComplete.draftOrder,
      },
    });
  } catch (err) {
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

const SEND_DRAFT_ORDER_INVOICE_MUTATION = `
  mutation draftOrderInvoiceSend($id: ID!) {
    draftOrderInvoiceSend(id: $id) {
      draftOrder {
        id
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// Mutation for adding metfields in the current draft order
const UPDATE_DRAFT_ORDER_PAYMENT_MUTATION = `
  mutation draftOrderUpdate($input: DraftOrderInput!, $id: ID!) {
    draftOrderUpdate(input: $input, id: $id) {
      draftOrder {
        id
        metafields(first: 10) {
          edges {
            node {
              id
              namespace
              key
              value
              type
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// Query for checking the status of current draft order
const GET_DRAFT_ORDER_STATUS = `
  query getDraftOrder($id: ID!) {
    draftOrder(id: $id) {
      id
      status
    }
  }
`;

// Mutation to change the status of current draft order to COMPLETED
const COMPLETE_DRAFT_ORDER_MUTATION = `
  mutation completeDraftOrder($id: ID!) {
    draftOrderComplete(id: $id) {
      draftOrder {
        id
        status
      }
      userErrors {
        field
        message
      }
    }
  }
`;