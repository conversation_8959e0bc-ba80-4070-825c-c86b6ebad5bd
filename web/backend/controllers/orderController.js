import shopify from "../../shopify.js";
import { getATFromSQL } from "../utils/getToken.js";

export const getOrderById = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Order ID is required.",
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(GET_ORDER_BY_ID_QUERY, {
      variables: { id: `gid://shopify/Order/${id}` },
    });

    if (!data.order) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: "Order not found.",
          },
        ],
      });
    }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        order: data.order,
      },
    });
  } catch (err) {
    console.error("Error fetching order:", err);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

export const sendOrderInvoice = async (req, res) => {
  try {
    const { email } = req.body;
    const orderId = req.params.id; // Assuming orderId is passed as a URL parameter, adjust as needed for your reques

    if (!orderId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Order ID is required.",
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(SEND_ORDER_INVOICE_MUTATION, {
      variables: {
        orderId: `gid://shopify/Order/${orderId}`,
        email,
      },
    });

    if (data.orderInvoiceSend.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: data.orderInvoiceSend.userErrors.map((error) => ({
          code: "VALIDATION_ERROR",
          message: error.message,
          field: error.field,
        })),
      });
    }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        order: data.orderInvoiceSend.order,
      },
    });
  } catch (err) {
    console.error("Error sending order invoice:", err);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

const SEND_ORDER_INVOICE_MUTATION = `
    mutation OrderInvoiceSend($orderId: ID!, $email: EmailInput) {
      orderInvoiceSend(id: $orderId, email: $email) {
        order {
          id
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

const GET_ORDER_BY_ID_QUERY = `
  query getOrder($id: ID!) {
    order(id: $id) {
      id
      name
      createdAt
      displayFinancialStatus
      displayFulfillmentStatus
      netPayment
      billingAddress {
        name
        address1
        address2
        city
        country
        countryCode
        phone
        province
        provinceCode
        zip
      }
      shippingAddress {
        address1
        address2
        city
        country
        countryCode
        phone
        province
        provinceCode
        zip
      }
      lineItems(first: 10) {
        nodes {
          image {
            src
          }
          sku
          quantity
          originalTotal
          originalUnitPrice
          name
          customAttributes {
            key
            value
          }
          variantTitle
          variant {
            selectedOptions {
              name
              value
            }
          }
        }
      }
      customAttributes {
        key
        value
      }
      subtotalPrice
      shippingLines(first: 10) {
        nodes {
          price
          id
          carrierIdentifier
        }
      }
      totalPrice
      totalDiscounts
      totalReceived
    }
  }
`;
