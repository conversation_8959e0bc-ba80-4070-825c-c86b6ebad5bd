import shopify from "../../shopify.js";
import { getATFromSQL } from "../utils/getToken.js";

export const searchCustomers = async (req, res) => {
  try {
    const { query } = req.query;
    if (!query) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Search query is required.",
          },
        ],
      });
    }

    let storeData = await getATFromSQL();
    let shop = process.env.SHOP;
    let AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const client = new shopify.api.clients.Graphql({
      session: {
        shop: shop,
        accessToken: AT,
      },
    });

    const response = await client.request(SEARCH_CUSTOMERS_QUERY, {
      variables: {
        query: query,
      },
    });

    res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        customers: response.data.customers.nodes,
      },
    });
  } catch (err) {
    console.error("Error searching customers:", err);
    res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

export const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;
    if (!id) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Customer ID is required.",
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(GET_CUSTOMER_BY_ID_QUERY, {
      variables: { id: `gid://shopify/Customer/${id}` },
    });

    if (!data.customer) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: "Customer not found.",
          },
        ],
      });
    }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        customer: data.customer,
      },
    });
  } catch (err) {
    console.error("Error fetching customer:", err);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

export const createCustomer = async (req, res) => {
  try {
    const { email, firstName, lastName, phone, addresses } = req.body;

    // Validate required fields
    const requiredFields = [
      "email",
      "firstName",
      "lastName",
      "phone",
      "addresses",
    ];
    const addressRequiredFields = [
      "address1",
      "address2",
      "city",
      "firstName",
      "lastName",
      "phone",
      "zip",
    ];

    const missingFields = requiredFields.filter((field) => !req.body[field]);

    if (missingFields.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: `Missing required fields: ${[...missingFields].join(
              ", "
            )}`,
          },
        ],
      });
    }

    const missingAddressFields = addressRequiredFields.filter(
      (field) => !req.body.addresses[field]
    );

    if (missingAddressFields.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: `Missing required fields: ${[
              ...missingAddressFields.map((f) => `addresses.${f}`),
            ].join(", ")}`,
          },
        ],
      });
    }

    const storeData = await getATFromSQL();
    const shop = process.env.SHOP;
    const AT = storeData.find((x) => x.shop === shop)?.accessToken;

    if (!AT) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Access token not found for the specified shop.",
          },
        ],
      });
    }

    const { data } = await new shopify.api.clients.Graphql({
      session: { shop, accessToken: AT },
    }).request(CREATE_CUSTOMER_MUTATION, {
      variables: {
        input: {
          email,
          firstName,
          lastName,
          phone,
          addresses,
        },
      },
    });

    if (data.customerCreate.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: data.customerCreate.userErrors.map((error) => ({
          code: "VALIDATION_ERROR",
          message: error.message,
        })),
      });
    }

    return res.status(201).json({
      responseCode: 0,
      status: "success",
      data: {
        customer: data.customerCreate.customer,
      },
    });
  } catch (err) {
    console.error("Error creating customer:", err);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: err.message,
        },
      ],
    });
  }
};

const CREATE_CUSTOMER_MUTATION = `
    mutation customerCreate($input: CustomerInput!) {
      customerCreate(input: $input) {
        customer {
          id
          firstName
          email
          lastName
          phone
          addressesV2(first: 10) {
            nodes {
              address1
              address2
              city
              country
              countryCode
              firstName
              id
              lastName
              phone
              province
              provinceCode
              zip
            }
          }
          orders(first: 10) {
            nodes {
              id
              name
              createdAt
              totalPrice
              displayFulfillmentStatus
              displayFinancialStatus
              lineItems(first: 10) {
                nodes {
                  id
                  quantity
                }
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }
  `;

const SEARCH_CUSTOMERS_QUERY = `
  query searchCustomers($query: String!) {
    customers(query: $query, first: 10) {
      nodes {
        id
        firstName
        email
        lastName
        phone
      }
    }
  }
`;

const GET_CUSTOMER_BY_ID_QUERY = `query searchCustomers($id: ID!) {
  customer(id: $id) {
    id
    firstName
    email
    lastName
    phone
    addressesV2(first: 10) {
      nodes {
        address1
        address2
        city
        country
        countryCode
        firstName
        id
        lastName
        phone
        province
        provinceCode
        zip
      }
    }
    orders(first: 10) {
      nodes {
        id
        name
        createdAt
        totalPrice
        displayFulfillmentStatus
        displayFinancialStatus
        lineItems(first: 10) {
          nodes {
            id
            quantity
          }
        }
      }
    }
  }
}
`;
