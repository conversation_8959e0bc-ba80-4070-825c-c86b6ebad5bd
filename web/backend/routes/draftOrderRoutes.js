import express from "express";
import {
  validateEndlessAisleDraftOrder,
  validateCartDraftOrder,
} from "../middlewares/draftOrderValidation.js";
import { handleDraftOrder, sendDraftOrderInvoice, createDraftOrderPayment } from "../controllers/draftOrderController.js";

const router = express.Router();

router.post("/cart", validateCartDraftOrder, handleDraftOrder);
router.post("/endless-aisle", validateEndlessAisleDraftOrder, handleDraftOrder);
router.put("/endless-aisle", validateEndlessAisleDraftOrder, handleDraftOrder);
router.post("/:id/send-invoice", sendDraftOrderInvoice);
router.put("/:id/payment", createDraftOrderPayment);

export default router;