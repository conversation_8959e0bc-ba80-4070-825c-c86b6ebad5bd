openapi: 3.0.0
info:
  title: Order API
  description: API endpoints for managing orders in Shopify store
  version: 1.0.0

servers:
  - url: /api

components:
  schemas:
    SuccessResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 0
        status:
          type: string
          example: "success"
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 1
        status:
          type: string
          example: "error"
        errors:
          type: array
          items:
            type: object
            properties:
              code:
                type: string
                enum: [BAD_REQUEST, VALIDATION_ERROR, NOT_FOUND, SERVER_ERROR]
              message:
                type: string

    Address:
      type: object
      properties:
        name:
          type: string
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        country:
          type: string
        countryCode:
          type: string
        phone:
          type: string
        province:
          type: string
        provinceCode:
          type: string
        zip:
          type: string

    Order:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        createdAt:
          type: string
        displayFinancialStatus:
          type: string
        displayFulfillmentStatus:
          type: string
        netPayment:
          type: string
        billingAddress:
          $ref: '#/components/schemas/Address'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        lineItems:
          type: object
          properties:
            nodes:
              type: array
              items:
                type: object
                properties:
                  image:
                    type: object
                    properties:
                      src:
                        type: string
                  sku:
                    type: string
                  quantity:
                    type: integer
                  originalTotal:
                    type: string
                  originalUnitPrice:
                    type: string
                  name:
                    type: string
                  customAttributes:
                    type: array
                    items:
                      type: object
                      properties:
                        key:
                          type: string
                        value:
                          type: string
        customAttributes:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
              value:
                type: string
        subtotalPrice:
          type: string
        shippingLines:
          type: object
          properties:
            nodes:
              type: array
              items:
                type: object
                properties:
                  price:
                    type: string
                  id:
                    type: string
                  carrierIdentifier:
                    type: string
        totalPrice:
          type: string
        totalDiscounts:
          type: string

paths:
  /orders/{id}:
    get:
      tags:
        - Orders
      summary: Get order by ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: Order ID
      responses:
        '200':
          description: Order found
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      order:
                        $ref: '#/components/schemas/Order'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "BAD_REQUEST"
                        message:
                          type: string
                          example: "Order ID is required."
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "NOT_FOUND"
                        message:
                          type: string
                          example: "Order not found."
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string

  /orders/{id}/send-invoice:
    post:
      tags:
        - Orders
      summary: Send order invoice
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: Order ID
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: Email address to send the invoice to (optional)
      responses:
        '200':
          description: Invoice sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      order:
                        type: object
                        properties:
                          id:
                            type: string
                            example: "gid://shopify/Order/12345678"
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "VALIDATION_ERROR"
                        message:
                          type: string
                        field:
                          type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string