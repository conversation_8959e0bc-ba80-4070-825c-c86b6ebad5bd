openapi: 3.0.0
info:
  title: Customer API
  description: API endpoints for managing customers in Shopify store
  version: 1.0.0

servers:
  - url: /api

components:
  schemas:
    SuccessResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 0
        status:
          type: string
          example: "success"
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 1
        status:
          type: string
          example: "error"
        errors:
          type: array
          items:
            type: object
            properties:
              code:
                type: string
                enum: [BAD_REQUEST, VALIDATION_ERROR, NOT_FOUND, SERVER_ERROR]
              message:
                type: string

    Customer:
      type: object
      properties:
        id:
          type: string
        firstName:
          type: string
        email:
          type: string
        lastName:
          type: string
        phone:
          type: string
        addressesV2:
          type: object
          properties:
            nodes:
              type: array
              items:
                type: object
                properties:
                  address1:
                    type: string
                  address2:
                    type: string
                  city:
                    type: string
                  country:
                    type: string
                  countryCode:
                    type: string
                  firstName:
                    type: string
                  id:
                    type: string
                  lastName:
                    type: string
                  phone:
                    type: string
                  province:
                    type: string
                  provinceCode:
                    type: string
                  zip:
                    type: string
        orders:
          type: object
          properties:
            nodes:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  createdAt:
                    type: string
                  totalPrice:
                    type: string
                  displayFulfillmentStatus:
                    type: string
                  displayFinancialStatus:
                    type: string
                  lineItems:
                    type: object
                    properties:
                      nodes:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            quantity:
                              type: integer

paths:
  /customers/search:
    get:
      tags:
        - Customers
      summary: Search customers
      parameters:
        - in: query
          name: query
          required: true
          schema:
            type: string
          description: Search query string
      responses:
        '200':
          description: Successful search
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      customers:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            firstName:
                              type: string
                            email:
                              type: string
                            lastName:
                              type: string
                            phone:
                              type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "BAD_REQUEST"
                        message:
                          type: string
                          example: "Search query is required."
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string

  /customers/{id}:
    get:
      tags:
        - Customers
      summary: Get customer by ID
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: Customer ID
      responses:
        '200':
          description: Customer found
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      customer:
                        $ref: '#/components/schemas/Customer'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "BAD_REQUEST"
                        message:
                          type: string
                          example: "Customer ID is required."
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "NOT_FOUND"
                        message:
                          type: string
                          example: "Customer not found."
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string

  /customers:
    post:
      tags:
        - Customers
      summary: Create new customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - firstName
                - lastName
                - phone
                - addresses
              properties:
                email:
                  type: string
                firstName:
                  type: string
                lastName:
                  type: string
                phone:
                  type: string
                addresses:
                  type: object
                  required:
                    - address1
                    - address2
                    - city
                    - firstName
                    - lastName
                    - phone
                    - zip
                  properties:
                    address1:
                      type: string
                    address2:
                      type: string
                    city:
                      type: string
                    firstName:
                      type: string
                    lastName:
                      type: string
                    phone:
                      type: string
                    zip:
                      type: string
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      customer:
                        allOf:
                          - $ref: '#/components/schemas/Customer'
                          - example:
                              id: "gid://shopify/Customer/12345678"
                              firstName: "John"
                              lastName: "Doe"
                              email: "<EMAIL>"
                              phone: "+1234567890"
                              addressesV2:
                                nodes:
                                  - address1: "123 Main St"
                                    address2: "Apt 4B"
                                    city: "New York"
                                    country: "United States"
                                    countryCode: "US"
                                    firstName: "John"
                                    id: "gid://shopify/MailingAddress/87654321"
                                    lastName: "Doe"
                                    phone: "+1234567890"
                                    province: "New York"
                                    provinceCode: "NY"
                                    zip: "10001"
                              orders:
                                nodes: []
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "VALIDATION_ERROR"
                        message:
                          type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string