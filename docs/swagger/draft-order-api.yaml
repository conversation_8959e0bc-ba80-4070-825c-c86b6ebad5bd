openapi: 3.0.0
info:
  title: Draft Order API
  description: API endpoints for managing draft orders in Shopify store
  version: 1.0.0

servers:
  - url: /api

components:
  schemas:
    SuccessResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 0
        status:
          type: string
          example: "success"
        data:
          type: object

    ErrorResponse:
      type: object
      properties:
        responseCode:
          type: integer
          example: 1
        status:
          type: string
          example: "error"
        errors:
          type: array
          items:
            type: object
            properties:
              code:
                type: string
                enum: [BAD_REQUEST, VALIDATION_ERROR, NOT_FOUND, SERVER_ERROR]
              message:
                type: string

    DraftOrderItem:
      type: object
      required:
        - variant_id
        - quantity
      properties:
        variant_id:
          type: string
          description: Shopify variant ID
          minLength: 14
          maxLength: 14
        quantity:
          type: integer
          description: Quantity of the item
          minimum: 1

    CustomerId:
      type: string
      description: Shopify customer ID

    Address:
      type: object
      required:
        - firstName
        - lastName
        - address1
        - countryCode
        - state
        - city
        - pincode
        - phone
      properties:
        firstName:
          type: string
          description: First name of the recipient
          minLength: 1
        lastName:
          type: string
          description: Last name of the recipient
          minLength: 1
        address1:
          type: string
          description: Primary address line
          minLength: 1
        address2:
          type: string
          description: Secondary address line (optional)
        countryCode:
          type: string
          description: Two-letter country code (ISO 3166-1 alpha-2)
          minLength: 2
          maxLength: 2
        state:
          type: string
          description: State/Province name
          minLength: 1
        city:
          type: string
          description: City name
          minLength: 1
        pincode:
          type: string
          description: Postal/ZIP code
          minLength: 1
        phone:
          type: string
          description: Contact phone number with country code
          pattern: "^[+]?[0-9]{10,15}$"
        email:
          type: string
          description: Email address of the recipient (optional)
          format: email
        company:
          type: string
          description: Company name (optional)

paths:
  /draft-orders/cart:
    post:
      tags:
        - Draft Orders
      summary: Create a new draft order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - items
              properties:
                items:
                  type: array
                  items:
                    $ref: "#/components/schemas/DraftOrderItem"
      responses:
        "200":
          description: Draft order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      checkoutUrl:
                        type: string
                        description: URL for the draft order checkout
                      message:
                        type: string
                        example: "Draft Order Created"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "INTERNAL_SERVER_ERROR"
                        message:
                          type: string
                          example: "Something went wrong"

  /draft-orders/endless-aisle:
    post:
      tags:
        - Draft Orders
      summary: Create a new draft order with customer information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - items
                - customerId
                - shippingAddress
              properties:
                items:
                  type: array
                  items:
                    $ref: "#/components/schemas/DraftOrderItem"
                customerId:
                  $ref: "#/components/schemas/CustomerId"
                shippingAddress:
                  $ref: "#/components/schemas/Address"
                billingAddress:
                  $ref: "#/components/schemas/Address"
      responses:
        "200":
          description: Draft order created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      draftOrder:
                        type: object
                        description: Draft order details returned from Shopify
        "400":
          description: Validation error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "INTERNAL_SERVER_ERROR"
                        message:
                          type: string
                          example: "Something went wrong"

  /draft-orders/{id}/send-invoice:
    post:
      tags:
        - Draft Orders
      summary: Send draft order invoice
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: Draft Order ID
      responses:
        '200':
          description: Draft Order Invoice sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    example: "gid://shopify/DraftOrder/1234567891011"
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "VALIDATION_ERROR"
                        message:
                          type: string
                        field:
                          type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 1
                  status:
                    type: string
                    example: "error"
                  errors:
                    type: array
                    items:
                      type: object
                      properties:
                        code:
                          type: string
                          example: "SERVER_ERROR"
                        message:
                          type: string
                          
  /draft-orders/{id}/payment:
    put:
      tags:
        - Draft Orders
      summary: Create payment for draft order and complete it
      description: Updates the draft order with payment details and marks it as completed
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          description: Draft Order ID
          example: "1234567891011"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                amount
              properties:
                amount:
                  type: number
                  description: Payment amount
                  minimum: 0
                paymentMethod:
                  type: string
                  description: Method of payment
                notes:
                  type: string
                  description: Additional payment notes
            examples:
              upi_payment:
                summary: UPI Payment Example
                value:
                  transactionMethod: "upi"
                  transactionDetails: "UPI_TXN_123456789"
              cash_payment:
                summary: Cash Payment Example
                value:
                  transactionMethod: "cash"
                  transactionDetails: "CASH_RECEIPT_987654321"
              card_payment:
                summary: Card Payment Example
                value:
                  transactionMethod: "card"
                  transactionDetails: "CARD_TXN_456789123"
      responses:
        '200':
          description: Payment processed successfully and draft order completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  responseCode:
                    type: integer
                    example: 0
                  status:
                    type: string
                    example: "success"
                  data:
                    type: object
                    properties:
                      draftOrder:
                        type: object
                        properties:
                          id:
                            type: string
                            example: "gid://shopify/DraftOrder/1234567891011"
                          status:
                            type: string
                            example: "COMPLETED"
        '400':
          description: Validation error or draft order already completed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                validation_error:
                  summary: Validation Error
                  value:
                    responseCode: 1
                    status: "error"
                    errors:
                      - code: "VALIDATION_ERROR"
                        message: "Invalid transaction method. Allowed methods are: cash, upi, card"
                empty_body:
                  summary: Empty Request Body
                  value:
                    responseCode: 1
                    status: "error"
                    errors:
                      - code: "VALIDATION_ERROR"
                        message: "Request body cannot be empty"
                completed_order:
                  summary: Already Completed Order
                  value:
                    responseCode: 1
                    status: "error"
                    errors:
                      - code: "VALIDATION_ERROR"
                        message: "Cannot update payment details for completed draft order"
        '401':
          description: Authentication error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                responseCode: 1
                status: "error"
                errors:
                  - code: "AUTH_ERROR"
                    message: "Access token not found for the specified shop."
        '404':
          description: Draft order not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                responseCode: 1
                status: "error"
                errors:
                  - code: "NOT_FOUND"
                    message: "Draft order not found"
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                responseCode: 1
                status: "error"
                errors:
                  - code: "SERVER_ERROR"
                    message: "Internal server error occurred"