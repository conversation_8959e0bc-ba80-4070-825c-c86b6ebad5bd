# Partial Payment Flow Documentation

This document provides a comprehensive overview of the partial payment flow for draft order creation in the application. The flow focuses on creating draft orders with partial payment calculations for jewelry products.

## Overview

The partial payment system allows customers to pay a portion of the total cost upfront for jewelry items. The payment is calculated based on different components of the jewelry (metal, diamonds, solitaires, gemstones, and making charges) with configurable percentages for each component.

## API Endpoints

The application exposes the following endpoints for draft order creation:

- **POST /api/draft-orders/cart**: Creates a draft order from cart items
- **POST /api/draft-orders/endless-aisle**: Creates a draft order with customer information (for in-store purchases)
- **PUT /api/draft-orders/endless-aisle**: Updates an existing draft order

## Flow Diagram

```
Client Request → Validation → Process Draft Order → Return Draft Order URL
                                    ↓
                    ┌───────────────┴───────────────┐
                    ↓                               ↓
            Fetch Metafields                Get Partial Payment
            & Material Prices                  Configuration
                    ↓                               ↓
                    └───────────────┬───────────────┘
                                    ↓
                        Calculate Partial Payments
                                    ↓
                          Create Draft Order
```

## Estimated API Response Time

- Get Metafields: 468ms
- Get Price: 50ms
- Perform Calculations: 25ms
- Get & Send Payload/Response: 300ms
- **Total**: Around 850-900ms

## Detailed Flow

### 1. Request Validation

The flow begins with validating the incoming request using middleware:

- `validateCartDraftOrder`: Validates requests to the `/cart` endpoint
- `validateEndlessAisleDraftOrder`: Validates requests to the `/endless-aisle` endpoint

These middlewares check:
- Cart items are valid (variant IDs and quantities)
- For endless aisle: customer ID and shipping address are valid

### 2. Draft Order Processing

The core processing happens in the `processDraftOrder` function:

```javascript
const processDraftOrder = async (req, customerInfo = null, draftOrderId) => {
  // Get store access token
  const shop = process.env.SHOP;
  const storeData = await getATFromSQL();
  const AT = storeData.find((x) => x.shop === shop)?.accessToken;

  // Extract variant IDs from request
  const variantIds = req.body.items.map((item) => item.variant_id);

  // Fetch all required data in parallel
  const [metafieldsWithVariantID, minCharges] = await Promise.all([
    getMetafields(shop, AT, variantIds),
    getPartialPaymentConfig(),
  ]);

  // Get materials and their prices
  const materialsToBeFetched = await getMetalsToFetchPrice(metafieldsWithVariantID);
  const materialPrices = await fetchMaterialPrices(materialsToBeFetched);

  // Calculate current prices and partial payments
  const currentPricesEachVariant = getCurrentPricesEachVariant(
    materialPrices,
    metafieldsWithVariantID
  );

  const partialPaymentForEachProduct = metafieldsWithVariantID.map((product) =>
    calculatePartialPayment(product, minCharges)
  );

  // Create draft order
  const createPartialPaymentUrl = await manageDraftOrder(
    currentPricesEachVariant,
    partialPaymentForEachProductMap,
    shop,
    AT,
    customerInfo || {},
    draftOrderId
  );

  return createPartialPaymentUrl;
};
```

### 3. Fetching Metafields

The system fetches product variant metafields using Shopify's GraphQL API:

```javascript
export const getMetafields = async (shop, AT, variantIds) => {
  const formattedIds = variantIds.map(
    (id) => `gid://shopify/ProductVariant/${id}`
  );

  // Query Shopify GraphQL API for variant metafields
  const response = await fetch(
    `https://${shop}/admin/api/2023-04/graphql.json`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      body: JSON.stringify({
        query: GET_VARIANT_METAFIELDS,
        variables: { ids: formattedIds },
      }),
    }
  );

  // Process and return metafields
  // ...
}
```

The metafields contain crucial information about the jewelry components:
- Metal type, purity, and weight
- Diamond clarity, color, type, and weights
- Solitaire details (shape, carat, color, clarity)
- Gemstone information

### 4. Fetching Material Prices

Based on the metafields, the system determines which material prices to fetch:

```javascript
export default async function fetchMaterialPrices(materialsToBeFetched) {
  const materialPrices = {};

  // Helper function to fetch data
  const fetchData = async (endpoint, key) => {
    try {
      const response = await fetch(`${process.env.BACKEND_URL}${endpoint}`, {
        method: "GET",
        redirect: "follow",
      });
      const result = await response.json();
      if (result) {
        materialPrices[key] = result;
      }
    } catch (error) {
      console.error(`Error fetching ${endpoint}:`, error);
    }
  };

  const fetchPromises = [];

  // Fetch metal prices if needed
  if (materialsToBeFetched.metalType) {
    const { metalType } = materialsToBeFetched;
    if (metalType === "Gold" || metalType === "Platinum") {
      fetchPromises.push(fetchData("/api/metal-price", "metal"));
    }
  }

  // Fetch diamond prices if needed
  if (materialsToBeFetched.diamondRound) {
    fetchPromises.push(fetchData("/api/diamond-price/round", "roundDiamond"));
  }
  if (materialsToBeFetched.diamondOther) {
    fetchPromises.push(fetchData("/api/diamond-price/others", "otherDiamond"));
  }

  // Fetch solitaire prices if needed
  const solitaireShapes = [
    materialsToBeFetched.solitaireShape1,
    materialsToBeFetched.solitaireShape2,
    materialsToBeFetched.solitaireShape3,
    materialsToBeFetched.solitaireShape4,
    materialsToBeFetched.solitaireShape5,
  ];
  const shapesSet = new Set(solitaireShapes.filter(Boolean));
  // Fetch Round if present
  if (shapesSet.has("Round")) {
    fetchPromises.push(fetchData("/api/solitaire-price/round", "roundSolitaire"));
  }

  // Check if there's any shape other than "Round"
  const hasOther = [...shapesSet].some((shape) => shape !== "Round");
  if (hasOther) {
    fetchPromises.push(fetchData("/api/solitaire-price/others", "otherSolitaire"));
  }

  // Handle gemstone type fetching based on the value of gemstoneType
  const gemstoneType = materialsToBeFetched.gemstoneType;
  if (gemstoneType) {
    switch (gemstoneType) {
      case "smallStudded":
        fetchPromises.push(fetchData("/api/gemstone-price/smallStudded", "gemstoneSmallStudded"));
        break;
      case "semiPrecious":
        fetchPromises.push(fetchData("/api/gemstone-price/semiPrecious", "gemstoneSemiPrecious"));
        break;
      case "Precious":
        fetchPromises.push(fetchData("/api/gemstone-price/precious", "gemstonePrecious"));
        break;
      default:
        break;
    }
  }

  // Wait for all fetch promises to complete
  await Promise.all(fetchPromises);

  return materialPrices;
}
```

#### Solitaire Price Calculation

```javascript
function calculateSolitairePrice(type, carat, grade) {
  const priceData = type === "round" ? roundSolitairePrices : otherSolitairePrices;

  const priceEntry = priceData.find(entry =>
    carat >= entry.minCarat && carat <= entry.maxCarat && entry.grade === grade
  );
  return priceEntry ? priceEntry.price : "Price not found";
}
```

#### Gemstone Price API

The system fetches gemstone prices from different endpoints based on the gemstone type:

- API Endpoint: `/api/gemstone-price/smallStudded` (for small studded gemstone)
- API Endpoint: `/api/gemstone-price/semiPrecious` (for semi precious gemstone)
- API Endpoint: `/api/gemstone-price/precious` (for precious gemstone)

Example of gemstone data structure:
```javascript
const gemstoneData = {
  smallStudded: [
    { _id: "67c56d969efd0b0f8a838007", gemstoneType: "smallStudded", minCarat: 0, maxCarat: 0.3, quality: "Quality 1", ratePerCt: 7500, shape: "round" },
    { _id: "67c56d969efd0b0f8a838008", gemstoneType: "smallStudded", minCarat: 0, maxCarat: 0.3, quality: "Quality 2", ratePerCt: 5000, shape: "round" },
    // More entries...
  ],
  semiPrecious: [
    // Entries for semi-precious gemstones
  ],
  precious: [
    // Entries for precious gemstones
  ]
};
```

Gemstone price calculation:
```javascript
const getGemstonePrice = (carat, gemstoneType, quality, shape) => {
  const gemstoneList = gemstoneData[gemstoneType] || [];

  const gemstone = gemstoneList.find(g =>
    carat >= g.minCarat && (g.maxCarat === null || carat < g.maxCarat) &&
    g.quality === quality && g.shape === shape
  );

  if (!gemstone) {
    return { error: "Gemstone not found for the given parameters." };
  }

  return {
    ratePerCt: gemstone.ratePerCt,
    totalPrice: gemstone.ratePerCt * carat
  };
};
```

### 5. Calculating Current Prices

The system calculates the current prices for each variant based on the material prices:

```javascript
export const getCurrentPricesEachVariant = (
  materialPrices,
  metafieldsWithVariantID
) => {
  // Calculate metal prices
  // Calculate diamond prices
  // Calculate solitaire prices
  // Calculate gemstone prices

  return metafieldsWithVariantID.map((variant) => {
    // Return structured price data for each variant
    return {
      variantId: variant.id,
      metal: { /* metal price details */ },
      diamond: { /* diamond price details */ },
      gemstone: { /* gemstone price details */ },
      solitaire: { /* solitaire price details */ },
    };
  });
};
```

### 6. Calculating Partial Payments

The system calculates partial payments based on configured percentages. This is done by applying the configured percentage to each component of the jewelry item:

```javascript
const calculatePartialPayment = (product, minCharges) => {
  const metal =
    parseFloat(product.totalPriceMetal?.value || 0) * minCharges.metal;
  const diamonds =
    parseFloat(product.totalPriceDiamonds?.value || 0) *
    minCharges.studdedDiamonds;
  const solitaire =
    parseFloat(product.totalPriceSolitaire?.value || 0) * minCharges.solitaire;
  const gemstones =
    parseFloat(product.totalGemstonePrice?.value || 0) * minCharges.gemstones;
  const making =
    parseFloat(product.totalMakingCharges?.value || 0) * minCharges.making;

  const subtotal = metal + diamonds + solitaire + gemstones + making;
  const gst = subtotal * 0.03;
  const totalWithGst = subtotal + gst;

  return {
    id: product.id,
    partialPayment: {
      metal,
      diamonds,
      solitaire,
      gemstones,
      making,
      subtotal,
      gst,
      totalWithGst,
    },
  };
};
```

The partial payment configuration is fetched from the backend:

```javascript
export const getPartialPaymentConfig = async () => {
  // Fetch configuration from API
  const response = await fetch(`${process.env.BACKEND_URL}/api/partial-payment-config`);
  const data = await response.json();

  if (data.responseCode === 0 && data.status === "success") {
    const config = data.data[0];

    // Helper function to convert percentage to decimal
    const toDecimal = (value) => {
      // If value is already in decimal form (e.g., 0.5)
      if (value >= 0 && value <= 1) {
        return value;
      }
      // If value is in percentage form (e.g., 50)
      if (value > 1) {
        return value / 100;
      }
      throw new Error(
        `Invalid value provided for partial payment config: ${value}`
      );
    };

    // Convert percentage values to decimal and validate
    return {
      metal: toDecimal(config.metal),
      making: toDecimal(config.making),
      studdedDiamonds: toDecimal(config.studdedDiamonds),
      solitaire: toDecimal(config.solitaire),
      gemstones: toDecimal(config.gemstones),
    };
  }
};
```

### 7. Creating the Draft Order

Finally, the system creates a draft order in Shopify using the GraphQL API:

```javascript
export const manageDraftOrder = async (
  currentPricesEachVariant,
  partialPaymentForEachProductMap,
  shop,
  AT,
  { customerId, shippingAddress, billingAddress },
  draftOrderId
) => {
  const orderAttributes = [];

  // Prepare line items with price overrides
  const lineItems = currentPricesEachVariant.map((variant) => {
    const { variantId } = variant;
    const partialPayment = partialPaymentForEachProductMap.get(variantId);

    orderAttributes.push({
      key: variantId.split("/").pop(),
      value: JSON.stringify(partialPayment),
    });

    return {
      variantId,
      quantity: 1,
      priceOverride: partialPayment
        ? {
            amount: partialPayment.totalWithGst.toString(),
            currencyCode: "INR",
          }
        : null,
      customAttributes: prepareLineItemAttribues(variant),
    };
  });

  const variables = {
    input: {
      customerId: customerId ? customerId : null,
      lineItems,
      shippingAddress: shippingAddress
        ? {
            firstName: shippingAddress.firstName,
            lastName: shippingAddress.lastName,
            address1: shippingAddress.address1,
            address2: shippingAddress.address2 || "",
            city: shippingAddress.city,
            province: shippingAddress.state,
            countryCode: "IN",
            zip: shippingAddress.pincode,
            phone: `${shippingAddress.phone}`,
          }
        : null,
      billingAddress: billingAddress
        ? {
            firstName: billingAddress.firstName,
            lastName: billingAddress.lastName,
            address1: billingAddress.address1,
            address2: billingAddress.address2 || "",
            city: billingAddress.city,
            province: billingAddress.state,
            countryCode: "IN",
            zip: billingAddress.pincode,
            phone: `${billingAddress.phone}`,
          }
        : null,
      customAttributes: orderAttributes,
    },
  };

  if (draftOrderId) {
    variables["ownerId"] = draftOrderId;
  }

  // Create or update draft order via Shopify GraphQL API
  const { data } = await new shopify.api.clients.Graphql({
    session: { shop, accessToken: AT },
  }).request(draftOrderId ? UPDATE_DRAFT_ORDER : CREATE_DRAFT_ORDER, {
    variables: variables,
  });

  // Return draft order data
  return {
    success: true,
    draftOrder: data.draftOrderCreate.draftOrder,
  };
};
```

The function `prepareLineItemAttribues` adds detailed information about each component of the jewelry item as line item attributes:

```javascript
export const prepareLineItemAttribues = ({
  metal,
  diamond,
  gemstone,
  solitaire,
}) => {
  return [
    { key: "metal_type", value: metal.type },
    { key: "metal_purity", value: metal.purity },
    { key: "metal_weight", value: metal.weight.toString() },
    { key: "metal_dhplPrice", value: metal.dhplPrice.toString() },
    { key: "metal_totalPrice", value: metal.totalMetalPrice.toString() },

    { key: "diamond_type", value: diamond.type },
    { key: "diamond_totalPrice", value: diamond.totalDiamondPrice.toString() },

    // Additional attributes for diamonds, gemstones, and solitaires
    // ...
  ];
};
```

The GraphQL mutation used to create the draft order:

```javascript
const CREATE_DRAFT_ORDER = `
  mutation createDraftOrder($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        invoiceUrl
        totalPrice
        customer {
          id
          email
          firstName
          lastName
          phone
        }
        subtotalPrice
        shippingLine {
          price
          code
          title
        }
        lineItems(first: 50) {
          nodes {
            id
            image {
              url
            }
            quantity
            title
            sku
            originalTotal
            variant {
              id
            }
          }
        }
      }
      userErrors {
        field
        message
      }
    }
  }`;
```

## Response Format

The API returns a standardized response format:

```json
{
  "responseCode": 0,
  "status": "success",
  "data": {
    "draftOrder": {
      "id": "gid://shopify/DraftOrder/123456789",
      "invoiceUrl": "https://shop.myshopify.com/...",
      "totalPrice": "10000.00",
      "customer": { /* customer details */ },
      "lineItems": { /* line items details */ }
    }
  }
}
```

## Error Handling

The application uses a centralized error handling middleware that formats errors consistently:

```json
{
  "responseCode": 1,
  "status": "error",
  "errors": [
    {
      "code": "VALIDATION_ERROR",
      "message": "Error message details"
    }
  ]
}
```

## Unexpected Cases & Error Handling

The system includes robust error handling for various scenarios:

1. **Metafields Not Found**: Returns a 404 Not Found error if metafields for a variant are missing.
2. **Price API Failure**: Implements a retry mechanism and returns a 502 Bad Gateway error if pricing data retrieval fails.
3. **Invalid Calculation**: If the partial total price calculation fails, logs the error and returns a 500 Internal Server Error.
4. **Draft Order Creation Failure**: If the Shopify API fails to create a draft order, returns an appropriate error response with detailed logging.

## Logic Part Workflow Summary

1. **Trigger API from Frontend**: The frontend sends a request with the cart payload containing product variant details.
2. **Fetch Metafields for Each Product Variant**: Retrieve metafields for each product variant in a single API call.
3. **Extract Unique Materials to Minimize API Calls**: Loop through the cart items to gather distinct materials to avoid duplicate API calls.
4. **Calculate Material Prices**: Perform price calculations based on weight, purity, and type.
5. **Retrieve Minimum Charges**: Fetch minimum charges from the configuration.
6. **Calculate Partial Payments**: Compute partial payments for each component.
7. **Generate New Object for Each Product Variant**: Create an object containing the calculated pricing details.
8. **Update Variant Attributes**: Update each variant's attributes with the computed values.
9. **Generate Checkout URL**: Create draft order with new values.
10. **Return Response**: Return the processed data with checkout URL for partial payment.

## Conclusion

The partial payment flow enables customers to pay a portion of the total cost for jewelry items. The system calculates this partial payment based on the components of the jewelry (metal, diamonds, solitaires, gemstones) and configurable percentages for each component.

The flow is implemented through a series of API endpoints that validate requests, fetch product information, calculate prices, and create draft orders in Shopify. By implementing proper validation, error handling, and security measures, this ensures a seamless user experience while maintaining system integrity.

The entire process is optimized to minimize API calls and provide fast response times, with an estimated total response time of around 850-900ms.
