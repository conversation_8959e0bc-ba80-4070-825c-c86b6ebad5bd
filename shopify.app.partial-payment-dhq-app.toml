# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "89b43753eacd90575aba2bb9dafabbc6"
name = "partial-payment-dhq-app"
handle = "partial-payment-dhq-app"
application_url = "https://investigator-certificate-populations-mls.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "diamond-hq-staging.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_customers,read_draft_orders,read_orders,read_products,write_customers,write_draft_orders,write_orders"

[auth]
redirect_urls = [
  "https://investigator-certificate-populations-mls.trycloudflare.com/auth/callback",
  "https://investigator-certificate-populations-mls.trycloudflare.com/auth/shopify/callback",
  "https://investigator-certificate-populations-mls.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2025-01"

[pos]
embedded = false
